# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
AIGen is a TypeScript-based question generation system for standardized reading comprehension tests. It uses Google's Gemini AI to generate various types of SAT-style reading questions with passages, multiple-choice answers, and explanations.

## Development Commands

### Build and Development
- `npm run build` - Compile TypeScript to JavaScript in `dist/`
- `npm run dev` - Run the main application in development mode using ts-node
- `npm run watch` - Watch for file changes and recompile automatically
- `npm start` - Run the compiled JavaScript from `dist/firebase.js`

### Testing
- `npm run test:blank-processor` - Run specific test for blank processor functionality
- Tests are located in the `test/` directory and use Node.js directly

## Core Architecture

### Main Components
1. **Question Generator (`src/question_generator.ts`)** - Main orchestrator that:
   - Manages parallel question generation using Gemini AI
   - Handles different question types and difficulty levels
   - Stores generated questions in Supabase database
   - Creates bug reports for failed parsing attempts

2. **Question Types System (`src/question_types.ts`)** - Defines question classes:
   - Base `ReadingQuestion` class with common functionality
   - Specialized classes for each question type (Word in Context, Main Idea, etc.)
   - Difficulty-specific prompts and validation logic

3. **Database Integration**:
   - **Firebase** (`src/firebase.ts`) - Firebase Admin SDK for authentication and Firestore
   - **Supabase** (`src/supabase.ts`) - Primary database for storing generated questions

### Question Types Supported
- Word in Context
- Main Idea  
- Specific Detail
- Main Purpose
- Main Purpose Underlined
- Overall Structure
- Inference
- Command of Evidence (support/undermine variants)
- Paired Passage

### AI Models Configuration
The system supports multiple Gemini models with configurable thinking budgets:
- `gemini-2.5-flash-preview-05-20` (with/without thinking)
- `gemini-2.5-pro-preview-03-25`
- `gemini-2.5-pro-exp-03-25`

### Data Flow
1. Question generation runs in parallel batches
2. Each question gets random topic (Social Science/Natural Science/Humanities) and difficulty
3. AI generates structured responses with passages, questions, choices, and explanations
4. Responses are parsed and validated before database storage
5. Failed parsing attempts create detailed bug reports in `bug_reports/`

### Environment Configuration
Required environment variables:
- `GEMINI_API_KEY` - Google Gemini AI API key
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `FB_PROJECT_ID`, `FB_CLIENT_EMAIL`, `FB_PRIVATE_KEY` - Firebase credentials

### File Structure
- `src/` - Main source code
- `test/` - Test files  
- `dist/` - Compiled JavaScript output
- `bug_reports/` - Generated error reports for debugging
- `static/` - Static assets

### Key Patterns
- Async/await with Promise.all for parallel processing
- Factory pattern for question type creation
- Structured AI prompt engineering with XML-like tags
- Comprehensive error handling with detailed bug reporting
- Database integration with both Firebase and Supabase