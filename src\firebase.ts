import dotenv from 'dotenv';
import pkg from 'firebase-admin';

dotenv.config();

const { FB_PROJECT_ID, FB_CLIENT_EMAIL, FB_PRIVATE_KEY } = process.env;


try {
    pkg.initializeApp({
        credential: pkg.credential.cert({
            projectId: FB_PROJECT_ID,
            clientEmail: FB_CLIENT_EMAIL, 
            privateKey: FB_PRIVATE_KEY,
        }),
    });
} catch (err) {
    if (err instanceof Error && !/already exists/u.test(err.message)) {
        console.error('Firebase Admin Error: ', err.stack);
    }
}

const db = pkg.firestore();
const auth = pkg.auth();

export { db, auth }; 

