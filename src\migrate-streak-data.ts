/**
 * Migration script to move streakData from users/{userId} field
 * to users/{userId}/missionProgress/streakData document
 *
 * This script:
 * 1. Reads existing streakData from users/{userId} documents
 * 2. Creates new streakData documents in users/{userId}/missionProgress/streakData
 * 3. Optionally removes the old streakData field after successful migration
 * 4. Is idempotent - can be run safely multiple times
 * 5. Includes proper error handling and logging
 */

import { db } from './firebase';
import pkg from 'firebase-admin';

interface UserStreakData {
  currentStreak: number;
  bestStreak: number;
  lastMissionDate?: string;
}

interface MigrationStats {
  totalUsers: number;
  usersWithStreakData: number;
  successfulMigrations: number;
  failedMigrations: number;
  alreadyMigrated: number;
  errors: Array<{ userId: string; error: string }>;
}

/**
 * Check if a user's streak data has already been migrated
 */
async function isAlreadyMigrated(userId: string): Promise<boolean> {
  try {
    const streakRef = db.collection('users').doc(userId).collection('missionProgress').doc('streakData');
    const streakDoc = await streakRef.get();
    return streakDoc.exists;
  } catch (error) {
    console.error(`Error checking migration status for user ${userId}:`, error);
    return false;
  }
}

/**
 * Migrate streak data for a single user
 */
async function migrateUserStreakData(
  userId: string,
  streakData: UserStreakData,
  removeOldField: boolean = false
): Promise<boolean> {
  try {
    // Check if already migrated
    if (await isAlreadyMigrated(userId)) {
      console.log(`User ${userId}: Already migrated, skipping`);
      return true;
    }

    // Create new streak data document
    const streakRef = db.collection('users').doc(userId).collection('missionProgress').doc('streakData');
    await streakRef.set(streakData);

    console.log(`User ${userId}: Successfully created new streak document`);

    // Optionally remove old field
    if (removeOldField) {
      const userRef = db.collection('users').doc(userId);
      await userRef.update({
        streakData: pkg.firestore.FieldValue.delete()
      });
      console.log(`User ${userId}: Removed old streakData field`);
    }

    return true;
  } catch (error) {
    console.error(`User ${userId}: Migration failed:`, error);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateAllStreakData(removeOldField: boolean = false): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalUsers: 0,
    usersWithStreakData: 0,
    successfulMigrations: 0,
    failedMigrations: 0,
    alreadyMigrated: 0,
    errors: []
  };

  console.log('Starting streak data migration...');
  console.log(`Remove old field after migration: ${removeOldField}`);

  try {
    // Get all users
    const usersRef = db.collection('users');
    const usersSnapshot = await usersRef.get();

    stats.totalUsers = usersSnapshot.size;
    console.log(`Found ${stats.totalUsers} users to process`);

    // Process each user
    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id;
      const userData = userDoc.data();

      // Check if user has streak data
      if (!userData.streakData) {
        continue;
      }

      stats.usersWithStreakData++;
      const streakData = userData.streakData as UserStreakData;

      console.log(`Processing user ${userId}:`, streakData);

      // Check if already migrated
      if (await isAlreadyMigrated(userId)) {
        stats.alreadyMigrated++;
        console.log(`User ${userId}: Already migrated`);
        continue;
      }

      // Migrate the user's streak data
      const success = await migrateUserStreakData(userId, streakData, removeOldField);
      
      if (success) {
        stats.successfulMigrations++;
      } else {
        stats.failedMigrations++;
        stats.errors.push({
          userId,
          error: 'Migration failed - check logs for details'
        });
      }

      // Add a small delay to avoid overwhelming Firestore
      await new Promise(resolve => setTimeout(resolve, 100));
    }

  } catch (error) {
    console.error('Error during migration:', error);
    stats.errors.push({
      userId: 'GLOBAL',
      error: `Global migration error: ${error}`
    });
  }

  return stats;
}

/**
 * Verify migration by checking a few users
 */
async function verifyMigration(): Promise<void> {
  console.log('\nVerifying migration...');
  
  try {
    const usersRef = db.collection('users');
    const usersSnapshot = await usersRef.get();

    let verifiedCount = 0;
    const maxVerifications = 5; // Check first 5 users with streak data

    for (const userDoc of usersSnapshot.docs) {
      if (verifiedCount >= maxVerifications) break;

      const userId = userDoc.id;
      const userData = userDoc.data();

      if (!userData.streakData) continue;

      // Check if new document exists
      const streakRef = db.collection('users').doc(userId).collection('missionProgress').doc('streakData');
      const streakDoc = await streakRef.get();

      if (streakDoc.exists) {
        const newStreakData = streakDoc.data();
        const oldStreakData = userData.streakData;
        
        console.log(`User ${userId}:`);
        console.log('  Old data:', oldStreakData);
        console.log('  New data:', newStreakData);
        console.log('  Match:', JSON.stringify(oldStreakData) === JSON.stringify(newStreakData));
        
        verifiedCount++;
      } else {
        console.log(`User ${userId}: New document not found!`);
      }
    }
  } catch (error) {
    console.error('Error during verification:', error);
  }
}

/**
 * Main execution function
 */
async function main() {
  const args = process.argv.slice(2);
  const removeOldField = args.includes('--remove-old-field');
  const verifyOnly = args.includes('--verify-only');

  if (verifyOnly) {
    await verifyMigration();
    return;
  }

  console.log('='.repeat(50));
  console.log('STREAK DATA MIGRATION SCRIPT');
  console.log('='.repeat(50));

  const stats = await migrateAllStreakData(removeOldField);

  console.log('\n' + '='.repeat(50));
  console.log('MIGRATION COMPLETE');
  console.log('='.repeat(50));
  console.log(`Total users: ${stats.totalUsers}`);
  console.log(`Users with streak data: ${stats.usersWithStreakData}`);
  console.log(`Already migrated: ${stats.alreadyMigrated}`);
  console.log(`Successful migrations: ${stats.successfulMigrations}`);
  console.log(`Failed migrations: ${stats.failedMigrations}`);

  if (stats.errors.length > 0) {
    console.log('\nErrors:');
    stats.errors.forEach(error => {
      console.log(`  ${error.userId}: ${error.error}`);
    });
  }

  // Run verification
  await verifyMigration();

  console.log('\nMigration script completed!');
  console.log('To remove old fields, run with --remove-old-field flag');
  console.log('To verify only, run with --verify-only flag');
}

// Run the migration
if (require.main === module) {
  main().catch(console.error);
}

export { migrateAllStreakData, verifyMigration };
