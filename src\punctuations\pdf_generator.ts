import PDFDocument from 'pdfkit';
import fs from 'fs';
import boundariesQuestions from '../../static/boundaries-questions.json';
import { createQuestion } from './punctuation_workflow';
import { QuestionData } from '../types.ts';
import { QuestionResult } from '../types.ts';

export function generateQuestionsPDF(questions: QuestionResult[], outputPath: string = 'generated_questions.pdf'): void {
    const doc = new PDFDocument({
        margin: 50,
        size: 'A4'
    });

    // Create a write stream
    doc.pipe(fs.createWriteStream(outputPath));

    // Add title
    doc.fontSize(20).font('Helvetica-Bold').text('SAT Writing & Language Practice Questions', { align: 'center' });
    doc.moveDown(2);

    questions.forEach((question, index) => {
        // Add page break for questions after the first
        if (index > 0) {
            doc.addPage();
        }

        // Question number and difficulty
        const difficultyMap = { 'E': 'Easy', 'M': 'Medium', 'H': 'Hard' };
        const difficultyText = difficultyMap[question.difficulty as keyof typeof difficultyMap] || question.difficulty;
        
        doc.fontSize(16).font('Helvetica-Bold').text(`Question ${index + 1}`, { underline: true, continued: true });
        doc.fontSize(12).font('Helvetica').fillColor('blue').text(` (${difficultyText})`, { underline: false });
        doc.fillColor('black');
        doc.moveDown(1);
        
        // Format passage with proper line breaks and indentation
        const formattedPassage = question.passage.replace(/_+/g, '________');
        doc.fontSize(11).font('Helvetica').text(formattedPassage, {
            align: 'justify',
            indent: 20,
            lineGap: 2
        });
        doc.moveDown(1);

        // Question text
        doc.fontSize(12).font('Helvetica-Bold').text('Which choice completes the text so that it conforms to the conventions of Standard English?');
        doc.moveDown(0.5);

        // Answer choices
        (question.choices ?? question.answerChoices).forEach((choice, choiceIndex) => {
            const letter = String.fromCharCode(65 + choiceIndex); // A, B, C, D
            doc.fontSize(11).font('Helvetica').text(`${letter}. ${choice}`, {
                indent: 20,
                lineGap: 1
            });
        });

        doc.moveDown(2);

        // Correct answer
        const correctLetter = String.fromCharCode(65 + question.correctAnswer);
        doc.fontSize(12).font('Helvetica-Bold').text(`Correct Answer: ${correctLetter}`, {
            fillColor: 'green'
        });
        doc.fillColor('black'); // Reset color
        doc.moveDown(1);

        // Explanation
        doc.fontSize(12).font('Helvetica-Bold').text('Explanation:');
        doc.moveDown(0.3);
        doc.fontSize(11).font('Helvetica').text(question.explanation, {
            align: 'justify',
            lineGap: 2
        });

        // Add some space at the bottom
        doc.moveDown(2);
    });

    // Finalize the PDF
    doc.end();
    console.log(`PDF generated successfully: ${outputPath}`);
}

export function generateAnswerKey(questions: QuestionResult[], outputPath: string = 'answer_key.pdf'): void {
    const doc = new PDFDocument({
        margin: 50,
        size: 'A4'
    });

    doc.pipe(fs.createWriteStream(outputPath));

    // Title
    doc.fontSize(18).font('Helvetica-Bold').text('Answer Key', { align: 'center' });
    doc.moveDown(2);

    // Create answer key table
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('Question #', 100, doc.y, { continued: true, width: 80 });
    doc.text('Correct Answer', 200, doc.y, { width: 100 });
    doc.moveDown(0.5);

    // Draw a line under headers
    doc.moveTo(100, doc.y).lineTo(400, doc.y).stroke();
    doc.moveDown(0.5);

    // Add answers
    doc.font('Helvetica');
    questions.forEach((question, index) => {
        const correctLetter = String.fromCharCode(65 + question.correctAnswer);
        doc.text(`${index + 1}`, 100, doc.y, { continued: true, width: 80 });
        doc.text(correctLetter, 200, doc.y, { width: 100 });
        doc.moveDown(0.3);
    });

    doc.end();
    console.log(`Answer key generated successfully: ${outputPath}`);
}

async function main() {
    // Convert JSON data to our interface format
    function convertJsonToQuestionData(jsonData: typeof boundariesQuestions[0]): QuestionData {
        return {
            originalPassage: jsonData.passage,
            originalChoices: jsonData.answerChoices,
            originalCorrectAnswerContent: jsonData.correctAnswer,
            difficulty: jsonData.difficulty,
            rationale: jsonData.rationale
        };
    }

    // Get 10 random questions from the boundaries questions
    const randomIndices = Array.from({length: boundariesQuestions.length}, (_, i) => i)
        .sort(() => Math.random() - 0.5)
        .slice(0, 10);
    
    const questions: QuestionData[] = randomIndices.map(index => 
        convertJsonToQuestionData(boundariesQuestions[index])
    );

    const allQuestions = await Promise.all(questions.map(createQuestion));
    const validQuestions = allQuestions.filter(q => q !== null && q !== undefined);
    if (validQuestions.length === 0) {
        console.error('No valid questions were generated');
        return;
    }

    console.log(`Generated ${validQuestions.length} valid questions.`);

    generateQuestionsPDF(validQuestions);
}


// main()