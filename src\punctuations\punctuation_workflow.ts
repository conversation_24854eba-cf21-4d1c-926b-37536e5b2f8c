import { genAI } from '../ai';
import { processPassageAndChoices } from './punctuations_blank_processor.ts';
import { validateQuestion } from './punctuations_validator.ts';
import { QuestionData } from '../types.ts';

export { QuestionData };

export async function createQuestion(data: QuestionData) {
    const { originalPassage, originalChoices, originalCorrectAnswerContent } = data;

    // Format originalPassage based on originalCorrectAnswer
    const formattedPassage = originalPassage.replace(/__+/, '<blank>' + originalCorrectAnswerContent + '</blank>');

    // Generate new passage based on original passage using Google GenAI
    const result = await genAI.models.generateContent({
        model: "gemini-2.5-pro",
        config: {
            temperature: 0.55,
            systemInstruction: {
                text: `You will be provided with a passage that has a section marked with \`<blank>...</blank>\`. Your task is to generate a new passage that follows the exact same punctuation pattern as the provided passage. In your new passage, mark the equivalent section with \`<blank>...</blank>\` as well. The blank must contain the punctuation like in the original.
                    \nThe new passage follows these rules:
                    \n- The passage topic is one of Social Science, Natural Science, or Humanities.
                    \n- Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.
                    \n- The passage refers to actual real studies. Therefore, it should also include the actual researcher’s name. The actual publish year is sometimes included. Do not make these up. Do not state where the study was published.
                    \n- The new passage has the same length as the original passage.
                    \n- MAINTAIN EXACT STRUCTURE. Do not add random clauses around the blank.
                    \n\nReturn the new passage. Do not return anything else.
                    `
            },
            thinkingConfig: {
                thinkingBudget: 128,
            },
            tools: [{googleSearch:{}}],
        },
        contents: [
            {
                role: "user",
                parts: [{ text: formattedPassage + "\n\nUse Grounding with Google Search." }]
            }
        ],
    });

    const newPassage = result.text!;

    // Process new passage to obtain new passage, choices, and correct answer
    const { modifiedPassage, shuffledChoices, correctAnswerIndex, error } = await processPassageAndChoices(newPassage, originalChoices);

    if (error) {
        throw new Error(error);
    }

    // Generate explanation
    const explanation = await generateExplanation(modifiedPassage, shuffledChoices, correctAnswerIndex, data.rationale);

    // Create the question object
    const questionObject = {
        passage: modifiedPassage,
        choices: shuffledChoices,
        correctAnswer: correctAnswerIndex,
        explanation: explanation,
        difficulty: data.difficulty
    };

    // Validate the question object before returning
    try {
        validateQuestion(questionObject);
    } catch (error) {
        throw new Error(`Generated question failed validation: ${(error as Error).message}`);
    }

    return questionObject;
}

async function generateExplanation(passage: string, choices: string[], correctAnswerIndex: number, rationale: string) {
    const config = {
        thinkingConfig: {
        thinkingBudget: 0,
        },
        responseMimeType: 'text/plain',
        systemInstruction: [
            {
            text: `# Punctuation Question Explanation\n\n
                    ## Introduction\n
                    You are an expert SAT tutor, skilled at explaining complex punctuation rules in a clear and accessible way. Your task is to provide detailed explanations for SAT punctuation questions, ensuring students understand not only the correct answer but also the reasoning behind it.\n\n
                    You will be provided with an SAT punctuation question and an example explanation from a totally different question. Your task is to fill in the template below with a clear and thorough explanation of why the answer is correct based on the example explanation.\n
                    ## Understanding Clauses and Sentence Structure\n\n
                    In the SAT, the choice of punctuations is only determined by the grammatical structure. The meaning of the passage rarely matters, unless stated otherwise.\n\n
                    Before creating punctuation questions, you must understand the fundamental difference between independent and dependent clauses:\n\n
                    ### Independent Clauses\n
                    - **Definition**: A complete sentence with at least a subject and predicate that can stand alone\n
                    - **Structure**: Subject (person/thing) + Predicate (what subject does, includes verb)\n
                    - **Example**: "The athlete runs."\n
                    - **Punctuation**: Usually ends with period or semicolon\n
                    - **Key Rule**: Two independent clauses CANNOT be separated by just a comma (creates comma splice error)\n\n
                    ### Dependent Clauses\n
                    - **Definition**: Clauses that cannot stand alone and must attach to independent clauses\n
                    - **Types**:\n
                    - **Essential Clauses**: Provide necessary information, no separating punctuation needed\n
                        - Example: "The astronaut who first walked on the Moon was Neil Armstrong."\n
                    - **Non-essential Clauses**: Provide additional information, must be separated by commas, dashes, or parentheses\n
                        - Example: "Recent surveys suggest that although most people still prefer paper—especially when reading intensively—attitudes are changing."\n\n
                    ### Common Dependent Clause Starters:\n
                    - **Relative Pronouns**: who, which, that\n
                    - **Subordinating Conjunctions**: because, although, when, while, since, before, after\n
                    - **Prepositions**: describing time or location\n
                    - **Appositives**: noun/pronoun explaining another noun\n
                    - **Participial Phrases**: beginning with present (-ing) or past (-ed) participles\n\n
                    ### Critical Punctuation Rule:\n
                    When a subordinating conjunction starts a sentence, place a comma after the dependent clause:\n
                    - "Because it does not really have another way of understanding them, the brain essentially regards letters as physical objects."\n
                    - When an independent clause comes first, the comma is optional: "The brain regards letters as objects because it has no other way of understanding them."\n
                    ## Core Punctuation Rules to Test\n\n
                    Based on SAT conventions, focus on these key punctuation concepts:\n\n
                    ### 1. Semicolons and Periods (Interchangeable)\n
                    - **Rule**: Semicolons = Periods on the SAT. This will ALWAYS be the case, no matter what.\n
                    - **Note**: The SAT does not require fine distinctions between these 2.\n
                    - **Usage**: Between two complete sentences (independent clauses)\n
                    - **Example**: "London is an old city; it has many new buildings."\n\n
                    ### 2. Colons and Dashes (Interchangeable)\n
                    - **Rule**: Used to introduce lists and explanations\n
                    - **Usage**: Must follow a complete sentence that logically sets up what follows\n
                    - **Example**: "They made an important discovery: the chemical compound was unstable."\n\n
                    ### 3. Comma + FANBOYS (For, And, Nor, But, Or, Yet, So)\n
                    - **Rule**: Comma + FANBOYS = Period = Semicolon\n
                    - **Note**: The SAT does not require fine distinctions between these 3.\n
                    - **Usage**: Connects two independent clauses (only exception is the Oxford Comma)\n
                    - **Example**: "London is old, but some parts are modern."\n
                    - **Common Error**: Comma splices (comma + pronoun without FANBOYS)\n\n
                    ### 4. Dependent Clauses\n
                    - **Rule**: When dependent clause comes first, follow with comma\n
                    - **Usage**: Subordinating conjunctions (although, because, when, until, while, since, before, after)\n
                    - **Example**: "Because London is old, it has buildings from many eras."\n\n
                    ### 5. Non-Essential Information\n
                    - **Rule**: Use matching punctuation pairs: 2 commas, 2 dashes, or 2 parentheses. In this case, Comma = Dash = Parentheses.\n
                    - **Note**: The SAT does not require fine distinctions between these 3.\n
                    - **Usage**: Information that can be removed without affecting sentence meaning\n
                    - **Example**: "London, which is very old, has modern parts."\n\n
                    ### 6. Names and Titles\n
                    - **Rule**: Either no commas (essential) or two commas (non-essential)\n
                    - **Test**: Remove the name/title - if sentence still makes sense, use two commas\n
                    - **Example**: "Engineering professor Vikram Iyer" vs. "A University of Washington professor, Vikram Iyer,"\n\n
                    ### 7. Additional Comma Rules\n
                    **Use commas for:**\n
                    - Items in list (semicolon can also do this)\n
                    - Separating reversible adjectives\n
                    - After introductory phrases\n\n
                    **DON'T use commas:**\n
                    - Before/after prepositions (of, to, by, from, for, about, with, in, on, at)\n
                    - Between subjects and verbs\n
                    - Between compound items linked by "and"\n
                    - Before/after "that"\n
                    - Between adjectives whose order cannot be reversed\n\n
                    ### 8. Question Marks\n
                    - **Direct questions**: Use question mark\n
                    - **Indirect questions**: Use period\n
                    - **Example**: "How much do babies understand?" vs. "Scientists study how much babies understand."\n\n
                    ## Explanation Templates:\n
                    Use the following template to structure your explanation:\n\n
                    ### For Clause Separation Questions:\n
                    (DO NOT RETURN THE ORIGINAL PASSAGE)\n
                    "Look at the clauses on both sides of the blank:"\n
                    - '[Clause 1]'\n
                    - '[Clause 2]'\n\n
                    [Determine if each clause is dependent or independent and choose the correct punctuation. Do not repeat the clauses. Instead, refer to them as "clause 1" and "clause 2"]\n\n
                    ### For Other Punctuation Questions:\n
                    "This question tests [specific punctuation rule]. The relevant rule is [explanation]. The correct answer is [letter] because [reason]."\n\n
                    ## Guidelines\n
                    Follow these guidelines:\n
                    * Begin by briefly restating the question and the correct answer.\n
                    * Explain the relevant punctuation rule or grammar principle that the question tests.\n
                    * Detail why the correct answer is correct, referencing specific parts of the question.\n
                    * Use clear, concise language that is easy for high school students to understand.\n
                    * Maintain a neutral and objective tone.\n
                    * Do not include any personal opinions or extraneous information.\n
                    * Ensure the explanation is comprehensive and covers all aspects of the question.\n
                    * DO NOT EXPLAIN WHY THE OTHER OPTIONS ARE INCORRECT.\n
                    * ONLY RETURN THE EXPLANATION. RETURN NOTHING ELSE. DO NOT RETURN THE ORIGINAL PASSAGE.`,
            }
        ],
    };
    const model = 'gemini-2.5-flash';
    const contents = [
        {
            role: 'user',
            parts: [
                {
                text: `
                    ${passage}\n
                    Which choice completes the text so that it conforms to the conventions of Standard English?\n
                    ${choices.map((choice, index) => `${String.fromCharCode(65 + index)}. ${choice}`).join('\n')}\n
                    Correct answer: ${String.fromCharCode(65 + correctAnswerIndex)}\n\n
                    Example Explanation:\n\n
                    ${rationale.replace(/Choice [A-D] is the best answer\. /g, '')}
                `,
                },
            ],
        },
    ];

    const response = await genAI.models.generateContent({
        model,
        config,
        contents,
    });


    return response.text;
}

