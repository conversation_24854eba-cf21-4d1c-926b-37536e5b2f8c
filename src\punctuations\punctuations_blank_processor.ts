#!/usr/bin/env node

import { Type } from "@google/genai";
import { genAI } from "../ai.ts";
import { ProcessResult } from '../types.ts';

export { ProcessResult };

const config = {
    temperature: 0,
    thinkingConfig: {
      thinkingBudget: 0,
    },
    responseMimeType: 'application/json',
    responseSchema: {
        type: Type.OBJECT,
        required: ["answerChoices"],
        properties: {
          answerChoices: {
              type: Type.ARRAY,
              items: {
                type: Type.STRING,
              },
          },
        },
    },
};
  
const model = 'gemini-2.5-flash';

/**
 * Tokenizes a string into words and punctuation
 * @param text - The text to tokenize
 * @returns Array of tokens (words and punctuation)
 */
function tokenize(text: string): string[] {
  return text.match(/\w+|[^\w\s]/g) || [];
}

/**
 * Determines if a token is a word or punctuation
 * @param token - The token to classify
 * @returns 'word' or 'punctuation'
 */
function getTokenType(token: string): 'word' | 'punctuation' {
  return /^\w+$/.test(token) ? 'word' : 'punctuation';
}

/**
 * Processes a passage containing a blank section and a list of answer choices.
 * 
 * @param {string} passage - The passage containing a <blank>...</blank> section to be processed
 * @param {string[]} choices - Array of answer choices
 * @returns {ProcessResult} Object containing modified passage with blanks and shuffled choices
 * @throws {Error} If no blank section is found or if choices have invalid format
 */
export async function processPassageAndChoices(passage: string, choices: string[]): Promise<ProcessResult> {  
  // Extract the blank content
  const blankMatch = passage.match(/<blank>(.*?)<\/blank>/s);
  if (!blankMatch) {
    throw new Error("No <blank>...</blank> section found in passage");
  }
  
  const blankContent = blankMatch[1].trim();
  
  // Replace the blank with 6 underscores
  let modifiedPassage = passage.replace(/<blank>.*?<\/blank>/s, '______');

  // Replace all newlines with spaces
  modifiedPassage = modifiedPassage.replace(/\n/g, ' ');
  
  // Modify answer choices
  const contents = `Input: ${blankContent}\nModify the answer choices below so that it looks like the input. Keep the original punctuation structure of the answer choices.\n\n`
    + choices.map((choice) => `    "${choice}"`).join('\n')
    + (choices.some((choice) => choice.includes('?')) ? "\n\nThe answer choices contains a question mark. Therefore, the modified answer choices can include question words like do, does, is, will, etc." : "");
 
  const response = await genAI.models.generateContent({
      model,
      config,
      contents,
  });

  const modifiedChoices: string[] = JSON.parse(response.text!).answerChoices;

  // Validate that modified choices have same token count and types as original choices
  for (let i = 0; i < choices.length; i++) {
    const originalTokens = tokenize(choices[i]);
    const modifiedTokens = tokenize(modifiedChoices[i]);
    
    if (originalTokens.length !== modifiedTokens.length) {
      return {
        error: `Choice ${i}: Token count mismatch. Original: ${originalTokens.length}, Modified: ${modifiedTokens.length}`
      };
    }
    
    for (let j = 0; j < originalTokens.length; j++) {
      const originalType = getTokenType(originalTokens[j]);
      const modifiedType = getTokenType(modifiedTokens[j]);
      
      if (originalType !== modifiedType) {
        return {
          error: `Choice ${i}, Token ${j}: Type mismatch. Original: "${originalTokens[j]}" (${originalType}), Modified: "${modifiedTokens[j]}" (${modifiedType})`
        };
      }
    }
  }

  // Shuffle the choices
  const shuffledChoices = [...modifiedChoices]
    .map(value => ({ value, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ value }) => value.trim());
  
  const correctAnswerIndex = shuffledChoices.indexOf(blankContent.trim());

  if (correctAnswerIndex === -1) {
    // Log error details to static folder
    const fs = require('fs');
    const path = require('path');
    const logDir = path.join(__dirname, '../static');
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    const logFile = path.join(logDir, 'blank_processor_errors.log');
    const logEntry = `
      Blank Content: ${blankContent.trim()}
      Modified Choices: ${JSON.stringify(modifiedChoices, null, 2)}
      Shuffled Choices: ${JSON.stringify(shuffledChoices, null, 2)}
      Original Passage: ${passage}
      Original Choices: ${JSON.stringify(choices, null, 2)}
    `;

    fs.appendFileSync(logFile, logEntry + '\n---\n');
  }

  return {
    modifiedPassage,
    shuffledChoices,
    correctAnswerIndex
  };
}