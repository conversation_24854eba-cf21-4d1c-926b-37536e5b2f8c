import { supabase } from "./supabase.ts";
import { Question } from './types.ts';

async function analyzeQuestions() {
    let allQuestions: Question[] = [];
    let page = 0;
    const pageSize = 1000;
    let hasMore = true;

    while (hasMore) {
        const { data: questions, error } = await supabase
            .from('question')
            .select('questionType, difficulty, topic')
            .range(page * pageSize, (page + 1) * pageSize - 1);

        if (error) {
            console.error('Error fetching questions:', error);
            return;
        }

        if (questions.length === 0) {
            hasMore = false;
        } else {
            allQuestions = [...allQuestions, ...questions];
            page++;
        }
    }

    // Count questions by type
    const questionCounts = allQuestions.reduce((acc, question) => {
        acc[question.questionType] = (acc[question.questionType] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    // Count unique combinations
    const combinationCounts = allQuestions.reduce((acc, question) => {
        const key = `${question.questionType}-${question.difficulty}-${question.topic}`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    console.log('\nQuestion counts by type:');
    console.log('------------------------');
    Object.entries(questionCounts).forEach(([type, count]) => {
        console.log(`${type}: ${count}`);
    });
    console.log('------------------------');
    console.log(`Total questions: ${allQuestions.length}`);

    console.log('\nQuestion counts by combination (Type-Difficulty-Topic):');
    console.log('--------------------------------------------------------');
    Object.entries(combinationCounts)
        .sort((a, b) => b[1] - a[1]) // Sort by count in descending order
        .forEach(([combination, count]) => {
            console.log(`${combination}: ${count}`);
        });
    console.log('--------------------------------------------------------');
    console.log(`Total unique combinations: ${Object.keys(combinationCounts).length}`);
}

// Run the analysis
analyzeQuestions().catch(error => {
    console.error('Error running analysis:', error);
});
