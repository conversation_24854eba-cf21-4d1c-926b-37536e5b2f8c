import { GoogleGenAI } from "@google/genai";
import fs from "node:fs";
import dotenv from 'dotenv';
import {
    WordInContextQuestion,
    MainIdeaQuestion,
    SpecificDetailQuestion,
    MainPurposeQuestion,
    InferenceQuestion,
    CommandOfEvidenceQuestion,
    PairedPassageQuestion,
    MainPurposeUnderlinedQuestion,
    OverallStructureQuestion,
    Question,
    StudentsNotesQuestion,
    TransitionsQuestion
} from './question_types.ts';
import { supabase } from "./supabase.ts";
import { generateQuestionsPDF } from "./punctuations/pdf_generator.ts";
import { generateReadingQuestionsPDF } from "./reading_pdf_generator.ts";
import { DifficultyLevel, ThinkingConfig, GenerationConfig, ModelConfig, TopicEntry } from './types.ts';
import topicsJSON from '../static/topics.json';

dotenv.config();

const apiKey = process.env.GEMINI_API_KEY;
const genAI = new GoogleGenAI({apiKey: apiKey});



// Question type factory
function createQuestion(type: string, topic: string, difficulty: DifficultyLevel, evidenceType: string | null = null): Question {
    switch (type) {
        case 'Word in Context':
            return new WordInContextQuestion(topic, difficulty);
        case 'Main Idea':
            return new MainIdeaQuestion(topic, difficulty);
        case 'Specific Detail':
            return new SpecificDetailQuestion(topic, difficulty);
        case 'Main Purpose':
            return new MainPurposeQuestion(topic, difficulty);
        case 'Main Purpose Underlined':
            return new MainPurposeUnderlinedQuestion(topic, difficulty);
        case 'Overall Structure':
            return new OverallStructureQuestion(topic, difficulty);
        case 'Inference':
            return new InferenceQuestion(topic, difficulty);
        case 'Command of Evidence':
            return new CommandOfEvidenceQuestion(topic, difficulty, evidenceType!);
        case 'Paired Passage':
            return new PairedPassageQuestion(topic, difficulty);
        case 'Students Notes':
            return new StudentsNotesQuestion(topic, difficulty);
        case 'Transitions':
            return new TransitionsQuestion(topic, difficulty);

        default:
            throw new Error(`Unknown question type: ${type}`);
    }
}

export async function run(questionType: string, modelType: string, evidenceType: string | null = null, numQuestions: number) {    
    // Create an array of promises for parallel execution
    const questionPromises = Array.from({ length: numQuestions }, async (_, i) => {
        // Array of possible topics and difficulties
        const topics = ['Social Science', 'Natural Science', 'Humanities'];
        const difficulties: DifficultyLevel[] = ['Easy', 'Medium', 'Hard'];

        // Randomly select topic and difficulty, but avoid certain combinations
        const rerollCombinations = [
            { topic: 'Humanities', difficulty: 'Hard' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Natural Science', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Social Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Social Science', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Humanities', difficulty: 'Easy' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Humanities', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Natural Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Idea' },
            { topic: 'Social Science', difficulty: 'Medium' as DifficultyLevel, type: 'Overall Structure' },
            { topic: 'Natural Science', difficulty: 'Medium' as DifficultyLevel, type: 'Main Purpose' },
            { topic: 'Natural Science', difficulty: 'Hard' as DifficultyLevel, type: 'Command of Evidence' },
            { topic: 'Humanities', difficulty: 'Easy' as DifficultyLevel, type: 'Inference' },
            { topic: 'Humanities', difficulty: 'Medium' as DifficultyLevel, type: 'Command of Evidence' },
        ]
        let topic = topics[Math.floor(Math.random() * topics.length)];
        let difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
        while (rerollCombinations.some(combination => combination.topic === topic && combination.difficulty === difficulty && combination.type === questionType)) {
            topic = topics[Math.floor(Math.random() * topics.length)];
            difficulty = difficulties[Math.floor(Math.random() * difficulties.length)];
        }

        // Select a specific topic instance
        const unusedTopics = (topicsJSON[topic] as TopicEntry[]).filter(t => !t.hasTopicBeenUsed);
        const topicEntry: TopicEntry = unusedTopics[Math.floor(Math.random() * unusedTopics.length)];
        
        // Create question instance
        const question = createQuestion(questionType, topic, difficulty, evidenceType);        

        // Create generation config based on modelType
        let modelName: string;

        // Randomize temperature between 1 and 2
        const randomTemperature = 1 + Math.random();

        const config: GenerationConfig = {
            temperature: randomTemperature,
            topP: 0.95,
            topK: 64,
            maxOutputTokens: 8192,
            responseModalities: [],
            responseMimeType: "text/plain",
        };

        switch (modelType) {
            case 'gemini-2.5-flash':
                config.thinkingConfig = {
                    thinkingBudget: 0,
                };
                modelName = 'gemini-2.5-flash';
                break;

            case 'gemini-2.5-flash (with thinking)':
                config.thinkingConfig = {
                    thinkingBudget: 512,
                };
                modelName = 'gemini-2.5-flash';
                break;

            case 'gemini-2.5-pro-preview-03-25':
            case "gemini-2.5-pro-exp-03-25":
                config.thinkingConfig = {
                    thinkingBudget: 0,
                };
                modelName = modelType;
                break;
            default:
                throw new Error(`Unknown model type: ${modelType}`);
        }

        // Get difficulty-specific prompt
        const prompt = question.generatePrompt(topicEntry.topic);

        const result = await genAI.models.generateContent({
            model: modelName,
            contents: [
                {
                    role: 'user',
                    parts: [{ text: prompt }]
                }
            ],
            config: {
                ...config,
                tools: [{googleSearch:{}}],
                systemInstruction: {
                    text: question.systemInstructions
                }
            }
        });

        let text: string = result.candidates?.[0]?.content?.parts?.filter((p: any) => p.text?.startsWith("<passage>")).map((p: any) => p.text)[0];

        if (!text) {
            text = result.text;
        }

        if (!text) {
            console.error(`Failed to parse generated question #${i} for ${questionType} - ${topic} - ${difficulty}`);
            return null;
        }

        // Parse the generated question
        const passageMatch = text.match(/<passage>[ |\n]*([\s\S]*?)[ |\n]*<\/passage>/);
        const passage2Match = text.match(/<passage_2>[ |\n]*([\s\S]*?)[ |\n]*<\/passage_2>/) ?? "";
        const questionMatch = text.match(/<question>[ |\n]*([\s\S]*?)[ |\n]*<\/question>/);
        const answerChoicesMatch = text.match(/<answer_choices>[ |\n]*((?:[A-D]\).*\n?){4})[ |\n]*<\/answer_choices>/);
        const correctAnswerMatch = text.match(/<correct_answer>[ |\n]*([A-D])\)?(?:\s*)[ |\n]*<\/correct_answer>/);
        const explanationMatch = text.match(/<explanation>[ |\n]*([\s\S]*?)[ |\n]*(?:<\/explanation>|$)/);

        if (passageMatch && questionMatch && answerChoicesMatch && correctAnswerMatch && explanationMatch) {
            const answerChoices = answerChoicesMatch[1].match(/^[A-D]\)\s*(.*)/gm)?.map((choice: string) => choice.replace(/^[A-D]\)\s*/, '').trim()) || [];

            const groundingChunks = result.candidates?.[0]?.groundingMetadata;
            let passage = passageMatch[1].trim();

            if (questionType === 'Students Notes') {
                passage = passage.replace(/\s*\|\|\s*/g, '\n');
            }

            const questionData = {
                model: modelName,
                topic: topic,
                questionType: questionType,
                difficulty: difficulty,
                intro: question.intro ?? "",
                passage: passage,
                passage2: questionType === 'Paired Passage' ? passage2Match[1].trim() : "",
                question: questionMatch[1].trim(),
                answerChoices: answerChoices,
                correctAnswer: ['A', 'B', 'C', 'D'].indexOf(correctAnswerMatch[1].trim()),
                explanation: explanationMatch[1].trim(),
                createdAt: new Date().toISOString(),
                groundingChunks: groundingChunks ?? []
            };

            // Validate answer choices
            const correctAnswerIndex = ['A', 'B', 'C', 'D'].indexOf(correctAnswerMatch[1].trim()[0]);
            if (!question.validateAnswerChoices(answerChoices, correctAnswerIndex)) {
                console.error(`Invalid answer choices or correct answer index for question #${i}`);
                return null;
            }

            // // Store in Supabase
            // const { error } = await supabase
            //     .from('question')
            //     .insert(questionData);

            // if (error?.details) {
            //     console.log(`Error storing question #${i}:`, error.details);
            //     return null;
            // }

            // Mark topic as used
            topicsJSON[topic].find(t => t.topic === topicEntry.topic)!.hasTopicBeenUsed = true;

            return questionData;
        } else {
            console.error(`Failed to parse generated question #${i} for ${questionType} - ${topic} - ${difficulty}`);
            const bugReport = {
                timestamp: new Date().toISOString().replace(/[-:Z]/g, '_'),
                inputParams: {
                    questionType,
                    topic,
                    difficulty,
                    evidenceType,
                    questionNumber: i,
                    modelType
                },
                parsingResults: {
                    hasPassage: !!passageMatch,
                    hasPassage2: !!passage2Match,
                    hasQuestion: !!questionMatch,
                    hasAnswerChoices: !!answerChoicesMatch,
                    hasCorrectAnswer: !!correctAnswerMatch,
                    hasExplanation: !!explanationMatch
                },
                systemState: {
                    systemInstructions: question.systemInstructions,
                    prompt: prompt,
                },
                rawResponse: result,
            };
            
            fs.writeFileSync(
                `bug_reports/${questionType.toLowerCase().replace(/\s+/g, '_')}_${difficulty}_${topic}_${bugReport.timestamp}.json`, 
                JSON.stringify(bugReport, null, 2)
            );
            return null;
        }
    });

    // Wait for all questions to complete
    const results = await Promise.all(questionPromises);
    
    // Filter out any null results (failed questions)
    const successfulQuestions = results.filter(result => result !== null);
    
    console.log(`Completed generating ${successfulQuestions.length} out of ${numQuestions} questions`);
    fs.writeFileSync('./static/topics.json', JSON.stringify(topicsJSON, null, 2));
    return successfulQuestions;
}

const questionTypes = [
    // { type: 'Word in Context', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 300 },
    // { type: 'Main Idea', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Specific Detail', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 200 },
    // { type: 'Main Purpose', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Main Purpose Underlined', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Overall Structure', model: "gemini-2.5-flash", evidenceType: null, numQuestions: 60 },
    // { type: 'Inference', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 250 },
    // { type: 'Command of Evidence', model: "gemini-2.5-flash (with thinking)", evidenceType: "support", numQuestions: 75 },
    // { type: 'Command of Evidence', model: "gemini-2.5-flash (with thinking)", evidenceType: "undermine", numQuestions: 75 },
    // { type: 'Paired Passage', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 200 },
    { type: 'Students Notes', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 8 },
    { type: 'Transitions', model: "gemini-2.5-flash (with thinking)", evidenceType: null, numQuestions: 8 },
];

// Run all question types in parallel
Promise.all(questionTypes.map(qt => {
    return run(qt.type, qt.model, qt.evidenceType, qt.numQuestions);
})).then(results => {
    console.log('All questions completed!');
    const totalSuccess = results.reduce((sum, arr) => sum + arr.length, 0);
    console.log(`Successfully generated ${totalSuccess} questions in total`);

    generateReadingQuestionsPDF(results.flat(), 'transitions_and_students_notes.pdf');
}).catch(error => {
    console.error('Error generating questions:', error);
});