// Centralized type definitions for AIGen project

// =======================
// Core Types
// =======================

export type DifficultyLevel = 'Easy' | 'Medium' | 'Hard';

// =======================
// Question Data Types
// =======================

export interface Question {
    questionType: string;
    difficulty: string;
    topic: string;
}

export interface ReadingQuestionData {
    model: string;
    topic: string;
    questionType: string;
    difficulty: string;
    intro: string;
    passage: string;
    passage2?: string;
    question: string;
    answerChoices: string[];
    correctAnswer: number;
    explanation: string;
    createdAt: string;
    groundingChunks?: any;
}

export interface QuestionResult {
    passage: string;
    choices?: string[];
    answerChoices?: string[];
    correctAnswer: number;
    explanation: string;
    difficulty: string;
}

export interface QuestionData {
    originalPassage: string;
    originalChoices: string[];
    originalCorrectAnswerContent: string;
    difficulty: string;
    rationale: string;
}

export interface QuestionObject {
    passage: string;
    choices: string[];
    correctAnswer: number;
    explanation: string;
    difficulty: string;
}

// =======================
// AI Model Configuration Types
// =======================

export interface ThinkingConfig {
    thinkingBudget: number;
}

export interface GenerationConfig {
    temperature: number;
    topP: number;
    topK: number;
    maxOutputTokens: number;
    responseModalities: any[];
    responseMimeType: string;
    thinkingConfig?: ThinkingConfig;
}

export interface ModelConfig {
    model: string;
    generationConfig: GenerationConfig;
    systemInstruction: string;
}

// =======================
// Processing Result Types
// =======================

export type ProcessResult = {
    modifiedPassage: string;
    shuffledChoices: string[];
    correctAnswerIndex: number;
} | {
    error: string;
}

export interface TopicEntry {
  topic: string;
  hasTopicBeenUsed: boolean;
}

export interface TopicsData {
  [category: string]: TopicEntry[];
}