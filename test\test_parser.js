import fs from 'node:fs';

function testParser(filePath) {
    try {
        // Read the test file
        const text = fs.readFileSync(filePath, 'utf8');
        console.log('Testing file:', filePath);
        console.log('----------------------------------------');

        // Test passage regex
        const passageMatch = text.match(/<passage>\n([\s\S]*?)<\/passage>/);
        console.log('Passage test:', passageMatch ? '✓ Success' : '✗ Failed');
        if (passageMatch) {
            console.log('Passage length:', passageMatch[1].trim().length, 'characters');
        }

        // Test question regex
        const questionMatch = text.match(/<question>\n([\s\S]*?)<\/question>/);
        console.log('Question test:', questionMatch ? '✓ Success' : '✗ Failed');
        if (questionMatch) {
            console.log('Question:', questionMatch[1].trim());
        }

        // Test answer choices regex
        const answerChoicesMatch = text.match(/<answer_choices>\n((?:[A-D]\).*\n?){4})<\/answer_choices>/);
        console.log('Answer choices test:', answerChoicesMatch ? '✓ Success' : '✗ Failed');
        if (answerChoicesMatch) {
            const answerChoices = answerChoicesMatch[1].match(/^[A-D]\)\s*(.*)/gm)
            .map(choice => choice.replace(/^[A-D]\)\s*/, '').trim());
            console.log('Number of choices found:', answerChoices.length);
            answerChoices.forEach(choice => {
                console.log('Choice:', choice.trim());
            });
        }

        // Test correct answer regex
        const correctAnswerMatch = text.match(/<correct_answer>\s*([A-D])\)?(?:\s*)<\/correct_answer>/);
        console.log('Correct answer test:', correctAnswerMatch ? '✓ Success' : '✗ Failed');
        if (correctAnswerMatch) {
            console.log('Correct answer:', correctAnswerMatch[1]);
        }

        // Test explanation regex
        const explanationMatch = text.match(/<explanation>\n([\s\S]*?)(?:<\/explanation>|$)/);
        console.log('Explanation test:', explanationMatch ? '✓ Success' : '✗ Failed');
        if (explanationMatch) {
            console.log('Explanation length:', explanationMatch[1].trim().length, 'characters');
        }

        // Overall validation
        const isValid = passageMatch && questionMatch && answerChoicesMatch && 
                       correctAnswerMatch && explanationMatch;
        console.log('\nOverall validation:', isValid ? '✓ All tests passed' : '✗ Some tests failed');

    } catch (error) {
        console.error('Error testing parser:', error);
    }
}

// Get the file path from command line arguments
const filePath = process.argv[2];
if (!filePath) {
    console.error('Please provide a file path as an argument');
    process.exit(1);
}

testParser(filePath); 