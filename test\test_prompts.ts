import {
    WordInContextQuestion,
    MainIdeaQuestion,
    SpecificDetailQuestion,
    MainPurposeQuestion,
    MainPurposeUnderlinedQuestion,
    InferenceQuestion,
    CommandOfEvidenceQuestion,
    PairedPassageQuestion,
    OverallStructureQuestion,
    StudentsNotesQuestion,
    TransitionsQuestion
} from '../src/question_types.ts';
import { DifficultyLevel } from '../src/types.ts';

// Test topics
const topics = ['Natural Science'];

// Test difficulties
const difficulties: DifficultyLevel[] = ['Medium'];

// Test each question type
function testQuestionType(QuestionClass, typeName) {
    console.log(`\n=== Testing ${typeName} ===\n`);
    
    topics.forEach(topic => {
        difficulties.forEach(difficulty => {
            console.log(`\n--- ${typeName} - ${topic} - ${difficulty} ---\n`);
            
            // Create question instance
            const question = new QuestionClass(topic, difficulty);
            
            // Generate and print prompt
            const prompt = question.generatePrompt("Sexy baka");
            console.log(prompt);
            console.log('\n--- End Prompt ---\n');
        });
    });
}

// Test Command of Evidence separately since it needs an additional parameter
function testCommandOfEvidence() {
    console.log('\n=== Testing Command of Evidence ===\n');
    
    topics.forEach(topic => {
        difficulties.forEach(difficulty => {
            ['support', 'undermine'].forEach(evidenceType => {
                console.log(`\n--- Command of Evidence - ${topic} - ${difficulty} - ${evidenceType} ---\n`);
                
                // Create question instance
                const question = new CommandOfEvidenceQuestion(topic, difficulty, evidenceType);
                
                // Generate and print prompt
                const prompt = question.generatePrompt("Sexy baka");
                console.log(prompt);
                console.log('\n--- End Prompt ---\n');
            });
        });
    });
}

// Run all tests
console.log('Starting prompt generation tests...\n');

// testQuestionType(WordInContextQuestion, 'Word in Context');
// testQuestionType(MainIdeaQuestion, 'Main Idea');
// testQuestionType(SpecificDetailQuestion, 'Specific Detail');
// testQuestionType(MainPurposeQuestion, 'Main Purpose');
// testQuestionType(MainPurposeUnderlinedQuestion, 'Main Purpose Underlined');
// testQuestionType(InferenceQuestion, 'Inference');
// testCommandOfEvidence();
// testQuestionType(PairedPassageQuestion, 'Paired Passage');
testQuestionType(TransitionsQuestion, 'Transitions');
testQuestionType(StudentsNotesQuestion, 'Students Notes');

// Test system instructions
console.log('\n=== Testing System Instructions ===\n');

topics.forEach(topic => {
    difficulties.forEach(difficulty => {
        console.log(`\n--- System Instructions - ${topic} - ${difficulty} ---\n`);
        
        // Create question instances for each type
        const questionTypes = [
            // new WordInContextQuestion(topic, difficulty),
            // new MainIdeaQuestion(topic, difficulty), 
            // new SpecificDetailQuestion(topic, difficulty),
            // new MainPurposeQuestion(topic, difficulty),
            // new MainPurposeUnderlinedQuestion(topic, difficulty),
            // new InferenceQuestion(topic, difficulty),
            // new CommandOfEvidenceQuestion(topic, difficulty, 'support'),
            // new PairedPassageQuestion(topic, difficulty),
            // new OverallStructureQuestion(topic, difficulty)
            new StudentsNotesQuestion(topic, difficulty),
            new TransitionsQuestion(topic, difficulty)
        ];

        // Print system instructions for each type
        questionTypes.forEach(question => {
            console.log(`\n${question.type} System Instructions:\n`);
            console.log(question.systemInstructions);
            console.log('\n--- End System Instructions ---\n');
        });
    });
});


console.log('\nAll tests completed!'); 