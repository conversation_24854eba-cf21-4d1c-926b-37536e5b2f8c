{"compilerOptions": {"target": "ES2024", "module": "commonjs", "lib": ["ES2024"], "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./", "moduleResolution": "node", "resolveJsonModule": true, "sourceMap": true, "noEmit": true, "allowImportingTsExtensions": true}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"]}